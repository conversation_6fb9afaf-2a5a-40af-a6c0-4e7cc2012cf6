<script lang="ts">
	import EmailThread from '$lib/components/conversation/EmailThread.svelte';
	import { onMount } from 'svelte';

	let loading = false;
	let threads: any[] = [];

	// Handle thread toggle events
	function handleThreadToggle(event: CustomEvent) {
		console.log('Thread toggled:', event.detail);
	}

	// Handle email click events
	function handleEmailClick(event: CustomEvent) {
		console.log('Email clicked:', event.detail);
		// Here you could open a detailed email view, mark as read, etc.
	}

	// Handle email action events
	function handleEmailAction(event: CustomEvent) {
		const { action, email } = event.detail;
		console.log(`Email action "${action}" triggered for:`, email);

		// Here you would implement the actual action logic
		switch (action) {
			case 'reply':
				alert(`Reply to email from ${email.sender.name}`);
				break;
			case 'replyAll':
				alert(`Reply All to email from ${email.sender.name}`);
				break;
			case 'forward':
				alert(`Forward email from ${email.sender.name}`);
				break;
			case 'addTask':
				alert(`Create task from email: ${email.subject}`);
				break;
			case 'addToCadence':
				alert(`Add email to cadence: ${email.subject}`);
				break;
			case 'instantBooker':
				alert(`Open instant booker for email: ${email.subject}`);
				break;
			case 'more':
				alert('Show more actions menu');
				break;
			default:
				console.log('Unknown action:', action);
		}
	}

	// Simulate loading state
	function simulateLoading() {
		loading = true;
		setTimeout(() => {
			loading = false;
		}, 2000);
	}

	onMount(() => {
		// Component will use mock data by default
	});
</script>

<svelte:head>
	<title>Email Thread</title>
</svelte:head>

<div class="bg-gray-100">
	<!-- Header -->
	<!-- <div class="bg-white shadow-sm border-b border-gray-200">
		<div class="px-6">
			<div class="flex justify-between items-center py-4">
				<div>
					<h1 class="text-2xl font-bold text-gray-900">Email Thread</h1>
				</div>
				<div class="flex space-x-3">
					<button
						on:click={simulateLoading}
						class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
					>
						Simulate Loading
					</button>
				</div>
			</div>
		</div>
	</div> -->

	<!-- Main content -->
	<div>
		<div class="flex">
			<!-- Customer List -->
			<div class="w-1/4">
				<div class="bg-white shadow-sm border border-gray-200 p-6 h-screen">
					<h3 class="text-lg font-medium text-gray-900 flex-grow">
						CPI
					</h3>
				</div>
			</div>

			<!-- Email Thread Component -->
			<div class="flex-1">
				<div class="bg-white shadow-sm border border-gray-200 flex flex-col h-screen">
					<div class="px-6 py-6 border-b border-gray-200 min-h-[120px]">
						<h2 class="text-lg font-medium text-gray-900">Header</h2>
					</div>
					
					<EmailThread
						{threads}
						{loading}
						on:threadToggle={handleThreadToggle}
						on:emailClick={handleEmailClick}
						on:emailAction={handleEmailAction}
					/>
				</div>
			</div>

			<!-- Info Panel -->
			<div class="w-1/4">
				<div class="bg-white shadow-sm border border-gray-200 p-6 h-screen">
					<h3 class="text-lg font-medium text-gray-900 flex-grow">
						Customer Info
					</h3>
				</div>
			</div>
		</div>
	</div>
</div>

<style>
	code {
		font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
	}
</style>
